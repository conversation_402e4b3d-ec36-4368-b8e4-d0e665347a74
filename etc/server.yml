gas.config:
  config_center:
    connect_timeout_seconds: 5
    namespaces:
      - group: "marketplace_others"
        project: "gameplatform"
        namespace: "rank_server_<ENV>_<CID>"
        non_live_secret: "f6be279298265eafa96ff9c502236962518528261548faefd2e9e3365aa68b84"
        live_secret: "c25d7fa8b8d3e2e2639644b80c85a0785b8b56380c4366dc1950f2050e43bd05"
      - group: "marketplace_others"
        project: "gameplatform"
        namespace: "rank_server_<ENV>_default"
        non_live_secret: "f6be279298265eafa96ff9c502236962518528261548faefd2e9e3365aa68b84"
        live_secret: "c25d7fa8b8d3e2e2639644b80c85a0785b8b56380c4366dc1950f2050e43bd05"



gas.engine:
  admin:
    enable_metrics: true
    enable_pprof: true
    submodule: gp_rank_admin
  timeouts:
    initialization: 20s


gas.grpc.client:
  x_protocol:
    non_live_service_key: ef63eebc9852132608d79b088bbed329
    service_name: game.rank
  clients:
    friend_grpc_client:
      x_protocol:
        enable: false
        dest_service: game_platform.friend
        service_key: "3d3864fae38757af72729ead665f8799"
    game_grpc_client:
      x_protocol:
        enable: false
        dest_service: game_platform.game_server
        service_key: "3d3864fae38757af72729ead665f8799"

gas.grpc.server:
  x_protocol:
    service_name: game.rank
    non_live_service_key: ef63eebc9852132608d79b088bbed329

  servers:
    game.rank:
      submodule: rank # required, have the server listen to PORT_xxx from env variable
      x_protocol:
        enable: true

gas.http.client:
  http_client: